{"name": "answer", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@tauri-apps/api": "^2.7.0", "@tauri-apps/plugin-opener": "^2", "axios": "^1.10.0", "element-plus": "^2.10.4", "exceljs": "^4.4.0", "js-image-compressor": "^2.0.0", "vue": "^3.5.13", "vue-router": "^4.5.1", "xlsx": "^0.18.5", "xlsx-populate": "^1.21.0"}, "devDependencies": {"@tauri-apps/cli": "^2", "@vitejs/plugin-vue": "^5.2.1", "vite": "^6.0.3"}}