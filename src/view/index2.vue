<template>
    <div>
      <input type="file" @change="handleFileUpload" accept=".xlsx, .xls" />
      <button @click="parseExcel">解析 Excel</button>
      
      <div v-if="fileContent.length > 0">
        <h3>文件内容：</h3>
        <table>
          <thead>
            <tr>
              <th v-for="(header, index) in headers" :key="index">{{ header }}</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(row, rowIndex) in fileContent" :key="rowIndex">
              <td v-for="(cell, cellIndex) in row" :key="cellIndex">{{ cell }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </template>
  
  <script setup>
import { ref } from 'vue';
import { read, utils } from 'xlsx';

const selectedFile = ref(null);
const fileContent = ref([]);
const headers = ref([]);

const handleFileUpload = (event) => {
  selectedFile.value = event.target.files[0];
};

const parseExcel = () => {
  if (!selectedFile.value) {
    alert('请先选择文件');
    return;
  }

  const reader = new FileReader();
  
  reader.onload = (e) => {
    try {
      const data = new Uint8Array(e.target.result);
      const workbook = read(data, { type: 'array' });
      const firstSheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[firstSheetName];
      const jsonData = utils.sheet_to_json(worksheet, { header: 1 });
      
      if (jsonData.length > 0) {
        headers.value = jsonData[0];
        fileContent.value = jsonData.slice(1);
      }
      console.log(fileContent.value,111122);
      
    } catch (error) {
      console.error('解析 Excel 文件出错:', error);
      alert('解析文件时出错: ' + error.message);
    }
  };
  
  reader.onerror = (error) => {
    console.error('文件读取失败:', error);
    alert('文件读取失败: ' + error.target.error.message);
  };
  
  // 以ArrayBuffer形式读取文件
  reader.readAsArrayBuffer(selectedFile.value);
};
</script>

  
  <style>
  table {
    border-collapse: collapse;
    width: 100%;
  }
  
  th, td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
  }
  
  th {
    background-color: #f2f2f2;
  }
  </style>