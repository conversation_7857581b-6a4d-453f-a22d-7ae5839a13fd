<template>
  <div class="container">
    <div class="upload-section">
      <h2>Excel 文件解析器</h2>
      <input type="file" @change="handleFileUpload" accept=".xlsx, .xls" />
      <br />
      <button @click="parseExcel">解析 Excel</button>
    </div>

    <div v-if="fileContent.length > 0">
      <h3>文件内容：</h3>
      <table>
        <thead>
          <tr>
            <th v-for="(header, index) in headers" :key="index">
              {{ header || `列 ${index + 1}` }}
            </th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(row, rowIndex) in fileContent" :key="rowIndex">
            <td v-for="(cell, cellIndex) in row" :key="cellIndex">
              <!-- 如果是图片单元格，显示图片 -->
              <img
                v-if="isImageCell(cell)"
                :src="cell"
                alt="图片"
                class="image-cell"
                @error="handleImageError"
              />
              <!-- 否则显示文本 -->
              <span v-else>{{ cell }}</span>
            </td>
          </tr>
        </tbody>
      </table>

      <!-- 显示解析后的JSON数据 -->
      <div v-if="parsedJsonData" class="json-section">
        <h3>解析后的JSON数据（UTF-8格式）：</h3>
        <pre>{{ formattedJsonData }}</pre>
        <div class="json-actions">
          <button @click="copyJsonToClipboard">复制JSON数据</button>
          <button @click="downloadJsonFile">下载JSON文件</button>
        </div>
      </div>
    </div>
  </div>
</template>
  
  <script setup>
import { ref, computed } from "vue";
import { read, utils } from "xlsx";

const selectedFile = ref(null);
const fileContent = ref([]);
const headers = ref([]);
const parsedJsonData = ref(null);

const handleFileUpload = (event) => {
  selectedFile.value = event.target.files[0];
};

// 判断是否为图片单元格
const isImageCell = (cell) => {
  if (typeof cell !== "string") return false;

  // 检查是否为base64图片
  if (cell.startsWith("data:image/")) return true;

  // 检查是否为HTTP/HTTPS图片URL
  if (cell.match(/^https?:\/\/.*\.(jpg|jpeg|png|gif|bmp|webp|svg)(\?.*)?$/i))
    return true;

  // 检查是否为本地图片路径
  if (cell.match(/\.(jpg|jpeg|png|gif|bmp|webp|svg)$/i)) return true;

  // 检查是否包含图片相关关键词
  if (
    cell.toLowerCase().includes("image") ||
    cell.toLowerCase().includes("图片")
  )
    return true;

  return false;
};

// 格式化JSON数据用于显示
const formattedJsonData = computed(() => {
  if (!parsedJsonData.value) return "";
  return JSON.stringify(parsedJsonData.value, null, 2);
});

const parseExcel = () => {
  if (!selectedFile.value) {
    alert("请先选择文件");
    return;
  }

  const reader = new FileReader();

  reader.onload = (e) => {
    try {
      const data = new Uint8Array(e.target.result);
      const workbook = read(data, {
        type: "array",
        cellHTML: false,
        cellNF: false,
        cellText: false,
        cellDates: true,
      });

      const firstSheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[firstSheetName];

      // 提取图片信息（如果存在）
      const images = extractImages(workbook, firstSheetName);

      // 解析为JSON数据，包含图片信息
      const jsonData = utils.sheet_to_json(worksheet, {
        header: 1,
        defval: "",
        blankrows: false,
      });

      if (jsonData.length > 0) {
        headers.value = jsonData[0];
        fileContent.value = jsonData.slice(1);

        // 处理图片数据，将图片信息合并到对应的单元格
        const processedData = processDataWithImages(jsonData, images);

        // 生成结构化的JSON数据
        const structuredData = generateStructuredJson(processedData);
        parsedJsonData.value = structuredData;

        // 以UTF-8格式打印JSON数据到控制台
        console.log("=== 解析后的JSON数据（UTF-8格式）===");
        console.log(JSON.stringify(structuredData, null, 2));
        console.log("=== JSON数据结束 ===");
      }
    } catch (error) {
      console.error("解析 Excel 文件出错:", error);
      alert("解析文件时出错: " + error.message);
    }
  };

  reader.onerror = (error) => {
    console.error("文件读取失败:", error);
    alert("文件读取失败: " + error.target.error.message);
  };

  // 以ArrayBuffer形式读取文件
  reader.readAsArrayBuffer(selectedFile.value);
};

// 提取Excel中的图片信息
const extractImages = (workbook, sheetName) => {
  const images = [];
  try {
    // 尝试从workbook中提取图片信息
    // 注意：xlsx库的图片提取功能有限，这里提供基础框架
    if (workbook.Sheets[sheetName]["!images"]) {
      images.push(...workbook.Sheets[sheetName]["!images"]);
    }

    // 另一种方法：扫描所有单元格，查找可能的图片URL或路径
    const worksheet = workbook.Sheets[sheetName];
    const range = utils.decode_range(worksheet["!ref"] || "A1:A1");

    for (let row = range.s.r; row <= range.e.r; row++) {
      for (let col = range.s.c; col <= range.e.c; col++) {
        const cellAddress = utils.encode_cell({ r: row, c: col });
        const cell = worksheet[cellAddress];

        if (cell && cell.v && typeof cell.v === "string") {
          // 检查是否为图片URL或路径
          if (isImageCell(cell.v)) {
            images.push({
              position: { row, col },
              data: cell.v,
              type: "url",
            });
          }
        }
      }
    }
  } catch (error) {
    console.warn("图片提取失败:", error);
  }
  return images;
};

// 处理数据，将图片信息合并到对应单元格
const processDataWithImages = (jsonData, images) => {
  const processedData = JSON.parse(JSON.stringify(jsonData));

  // 如果有图片信息，将其合并到对应的单元格
  images.forEach((image) => {
    if (image.position && image.data) {
      const row = image.position.row;
      const col = image.position.col;
      if (processedData[row] && processedData[row][col] !== undefined) {
        // 将图片数据转换为base64格式
        processedData[row][col] = `data:image/${image.ext || "png"};base64,${
          image.data
        }`;
      }
    }
  });

  return processedData;
};

// 生成结构化的JSON数据
const generateStructuredJson = (data) => {
  if (!data || data.length === 0) return null;

  const headers = data[0];
  const rows = data.slice(1);

  const structuredData = {
    metadata: {
      totalRows: rows.length,
      totalColumns: headers.length,
      headers: headers,
      parseTime: new Date().toISOString(),
      encoding: "UTF-8",
    },
    data: rows.map((row, index) => {
      const rowData = {
        rowIndex: index + 1,
        cells: {},
      };

      headers.forEach((header, colIndex) => {
        const cellValue = row[colIndex] || "";
        rowData.cells[header || `Column_${colIndex + 1}`] = {
          value: cellValue,
          type: detectCellType(cellValue),
          isImage: isImageCell(cellValue),
        };
      });

      return rowData;
    }),
  };

  return structuredData;
};

// 检测单元格数据类型
const detectCellType = (value) => {
  if (value === null || value === undefined || value === "") return "empty";
  if (typeof value === "number") return "number";
  if (typeof value === "boolean") return "boolean";
  if (value instanceof Date) return "date";
  if (isImageCell(value)) return "image";
  return "string";
};

// 复制JSON数据到剪贴板
const copyJsonToClipboard = async () => {
  if (!parsedJsonData.value) return;

  try {
    const jsonString = JSON.stringify(parsedJsonData.value, null, 2);
    await navigator.clipboard.writeText(jsonString);
    alert("JSON数据已复制到剪贴板！");
  } catch (error) {
    console.error("复制失败:", error);
    alert("复制失败，请手动复制");
  }
};

// 下载JSON文件
const downloadJsonFile = () => {
  if (!parsedJsonData.value) return;

  const jsonString = JSON.stringify(parsedJsonData.value, null, 2);
  const blob = new Blob([jsonString], {
    type: "application/json;charset=utf-8",
  });
  const url = URL.createObjectURL(blob);

  const link = document.createElement("a");
  link.href = url;
  link.download = `excel_data_${new Date().getTime()}.json`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};
</script>

  
  <style scoped>
.container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.upload-section {
  margin-bottom: 20px;
  padding: 20px;
  border: 2px dashed #ddd;
  border-radius: 8px;
  text-align: center;
}

.upload-section input[type="file"] {
  margin-bottom: 10px;
}

.upload-section button {
  background-color: #409eff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
}

.upload-section button:hover {
  background-color: #337ecc;
}

table {
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 20px;
}

th,
td {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
  vertical-align: top;
}

th {
  background-color: #f2f2f2;
  font-weight: bold;
}

.json-section {
  margin-top: 20px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background-color: #fafafa;
}

.json-section h3 {
  margin-top: 0;
  color: #333;
}

.json-section pre {
  max-height: 400px;
  overflow-y: auto;
  font-size: 12px;
  line-height: 1.4;
}

.json-actions {
  margin-top: 10px;
}

.json-actions button {
  margin-right: 10px;
  padding: 8px 16px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  cursor: pointer;
}

.json-actions button:hover {
  background-color: #f0f0f0;
}

.image-cell {
  max-width: 100px;
  max-height: 100px;
  object-fit: contain;
  border-radius: 4px;
}
</style>