<template>
      <div class="headerContent1">
      <img
        @click="goBack"
        src="../assets/cancel.png"
        alt="返回"
        class="back-btn1"
      />
      <span style="color: #000;">个人中心</span>
    </div>
  <div style="padding: 20px;">
    <div class="user-setting-container">
    <div class="cdk-label">
      CDK：
      <span v-if="!isEditing" class="cdk-value">{{ cdk || '未设置' }}</span>
      <el-input
        v-else
        v-model="editCdk"
        size="small"
        class="cdk-input"
        placeholder="请输入CDK"
        autofocus
      />
      <el-button
        v-if="!isEditing"
        type="primary"
        size="small"
        class="edit-btn"
        @click="startEdit"
      >修改</el-button>
      <el-button
        v-else
        type="success"
        size="small"
        class="save-btn"
        @click="saveCdk"
      >保存</el-button>
      <el-button
        v-if="isEditing"
        type="info"
        size="small"
        class="cancel-btn"
        @click="cancelEdit"
      >取消</el-button>
    </div>
  </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { setQuizBind } from "../api/answer";
import { ElMessage } from 'element-plus'
import { useRouter, useRoute } from "vue-router";
const route = useRoute();
const router = useRouter();
const cdk = ref(localStorage.getItem('skdToken1') || '')
const isEditing = ref(false)
const editCdk = ref(cdk.value)

function startEdit() {
  editCdk.value = cdk.value
  isEditing.value = true
}

function goBack() {
  router.push({ path: "/" });
}
function generateUid() {
  // 简单生成一个16位的随机字符串
  const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let uid = '';
  for (let i = 0; i < 16; i++) {
    uid += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return uid;
}
// iixybymhfvabchpv
function saveCdk() {
  if (!editCdk.value) {
    ElMessage.error('CDK不能为空') 
    return
  }
  cdk.value = editCdk.value
//   localStorage.setItem("deviceCode1",generateUid())
  let deviceCode1 = localStorage.getItem("deviceCode1");
  setQuizBind({ cdkey: cdk.value, device: deviceCode1 }).then((res) => {
    // 判断返回的code是否为200，表示绑定成功
    if (res.code === 200) {
        localStorage.setItem('skdToken1', cdk.value)
        isEditing.value = false
        ElMessage.success('CDK已保存')
    } else {
      // 绑定失败，弹窗提示
      localStorage.removeItem("skdToken1");
      ElMessage.error(res.message || "请输入正确的CDK")
    //   tittopTextRef.value?.open(res.message || "请输入正确的CDK");
    }
  });
  
}

function cancelEdit() {
  isEditing.value = false
}
</script>

<style scoped>
.user-setting-container {
  padding: 32px 24px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 12px #00000010;
  font-size: 16px;
}
.cdk-label {
  display: flex;
  align-items: center;
  gap: 10px;
}
.cdk-value {
  font-weight: bold;
  color: #409eff;
  min-width: 80px;
  word-break: break-all;
}
.cdk-input {
  width: 180px;
}
.edit-btn, .save-btn, .cancel-btn {
  margin-left: 8px;
}
.headerContent1 {
  display: flex;
  align-items: center;
  padding: 20px 20px 10px 20px;
  font-size: 20px;
  font-weight: bold;
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
}
.back-btn1 {
  width: 20px;
  height: 20px;
  margin-right: 15px;
  cursor: pointer;
}
</style>